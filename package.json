{"name": "udemy-demo-shop", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.13.0", "@prisma/extension-accelerate": "^2.0.2", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "prisma": "^6.13.0", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}