import ProductCard from "./ProductCard";
import { mockProducts, Product } from "../lib/mocks";

async function fetchProductsFromDatabase(): Promise<Product[]> {
    return mockProducts;
}

const Products = async () => {
    const products = await fetchProductsFromDatabase();
    const currency: "USD" = "USD";

    return (
        <div className="container mx-auto px-4 py-8">
            <h1 className="text-3xl font-bold mb-6 text-center">Nuestros Productos</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {products.map((product) => (
                    <ProductCard key={product.id} product={product} currency={currency} />
                ))}
            </div>
        </div>
    );
};

export default Products;