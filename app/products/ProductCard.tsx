import { Product } from "../lib/mocks";

import Image from "next/image";
import { formatPrice } from "../lib/utils";

export default function ProductCard({ product, currency }: { product: Product; currency: "USD" | "COP" }) {
  return (
    <div className="border border-gray-200 rounded-lg p-4">
      <div className="relative aspect-video overflow-hidden group">
        <Image
          src={product.image}
          alt={product.name}
          className="object-cover transition-transform duration-300 ease-in-out group-hover:scale-110"
          fill
          sizes="(max-width: 640px) 100vw, (min-width: 1280px) 33vw, 50vw"
        />
      </div>
      <h2 className="text-xl font-semibold mb-2 text-black">{product.name}</h2>
      <p className="text-gray-800 font-bold mb-2">{formatPrice(product.price)}</p>
      <p className="text-gray-500 text-sm text-center">{product.description}</p>
    </div>
  );
}
