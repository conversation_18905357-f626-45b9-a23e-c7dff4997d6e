import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
    await prisma.category.deleteMany()
    await prisma.product.deleteMany()

    const categories = [
        {
            name: "Electronics",
            slug: "electronics"
        },
        {
            name: "Clothing",
            slug: "clothing"
        },
        {
            name: "Home",
            slug: "home"
        }
    ]

    for (const category of categories) {
        await prisma.category.create({
            data: category
        })
    }
}

main()
    .then(async () => {
        console.log("Seed completed")
        await prisma.$disconnect()
    })
    .catch(async (e) => {
        console.error(e)
        await prisma.$disconnect()
        process.exit(1)
    })